package handler

import (
    "time"
    "github.com/golang-jwt/jwt/v5"
)

var jwtSecret = []byte("your-secret-key") // Change this to a secure value

// GenerateJWT generates a JWT token with the user's email
func GenerateJWT(email string) (string, error) {
    claims := jwt.MapClaims{
        "email": email,
        "exp":   time.Now().Add(time.Hour * 24).Unix(),
    }
    token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
    return token.SignedString(jwtSecret)
}

// ParseJWT parses and validates the JWT token, returning the email if valid
func ParseJWT(tokenString string) (string, error) {
    token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
        return jwtSecret, nil
    })
    if err != nil || !token.Valid {
        return "", err
    }
    claims, ok := token.Claims.(jwt.MapClaims)
    if !ok {
        return "", err
    }
    email, ok := claims["email"].(string)
    if !ok {
        return "", err
    }
    return email, nil
}