package model

import (
	"time"
)

// JobStatus represents the current status of a scraping job
type JobStatus string

const (
	JobStatusPending    JobStatus = "pending"
	JobStatusQueued     JobStatus = "queued"
	JobStatusRunning    JobStatus = "running"
	JobStatusCompleted  JobStatus = "completed"
	JobStatusFailed     JobStatus = "failed"
	JobStatusCancelled  JobStatus = "cancelled"
)

// JobPriority represents the priority level of a job
type JobPriority int

const (
	PriorityLow    JobPriority = 1
	PriorityNormal JobPriority = 2
	PriorityHigh   JobPriority = 3
	PriorityCritical JobPriority = 4
)

// ScrapingConfig contains configuration for the scraping job
type ScrapingConfig struct {
	URL             string            `json:"url" binding:"required" example:"https://example.com"`
	Selectors       map[string]string `json:"selectors" example:"{\"title\":\"h1\",\"price\":\".price\"}"`
	MaxPages        int               `json:"max_pages" example:"10"`
	DelayMs         int               `json:"delay_ms" example:"1000"`
	Headers         map[string]string `json:"headers,omitempty"`
	UserAgent       string            `json:"user_agent,omitempty" example:"Mozilla/5.0..."`
	JavaScriptEnabled bool            `json:"javascript_enabled" example:"false"`
	Timeout         int               `json:"timeout" example:"30"`
}

// Job represents a web scraping job
type Job struct {
	ID          string         `json:"id" example:"job_123456"`
	UserEmail   string         `json:"user_email" example:"<EMAIL>"`
	Name        string         `json:"name" binding:"required" example:"E-commerce Product Scraper"`
	Description string         `json:"description" example:"Scrape product information from e-commerce site"`
	Config      ScrapingConfig `json:"config"`
	Status      JobStatus      `json:"status" example:"pending"`
	Priority    JobPriority    `json:"priority" example:"2"`
	Progress    int            `json:"progress" example:"75"`
	CreatedAt   time.Time      `json:"created_at" example:"2023-07-18T10:30:00Z"`
	StartedAt   *time.Time     `json:"started_at,omitempty" example:"2023-07-18T10:35:00Z"`
	CompletedAt *time.Time     `json:"completed_at,omitempty" example:"2023-07-18T11:00:00Z"`
	ErrorMsg    string         `json:"error_msg,omitempty" example:"Connection timeout"`
	ResultCount int            `json:"result_count" example:"150"`
	ResultURL   string         `json:"result_url,omitempty" example:"https://storage.example.com/results/job_123456.json"`
}

// CreateJobRequest represents a request to create a new scraping job
type CreateJobRequest struct {
	Name        string         `json:"name" binding:"required" example:"Product Scraper"`
	Description string         `json:"description" example:"Scrape product data"`
	Config      ScrapingConfig `json:"config" binding:"required"`
	Priority    JobPriority    `json:"priority" example:"2"`
}

// UpdateJobRequest represents a request to update a job
type UpdateJobRequest struct {
	Name        *string        `json:"name,omitempty" example:"Updated Product Scraper"`
	Description *string        `json:"description,omitempty" example:"Updated description"`
	Config      *ScrapingConfig `json:"config,omitempty"`
	Priority    *JobPriority   `json:"priority,omitempty" example:"3"`
}

// JobSummary represents a lightweight view of a job
type JobSummary struct {
	ID          string      `json:"id" example:"job_123456"`
	Name        string      `json:"name" example:"Product Scraper"`
	Status      JobStatus   `json:"status" example:"running"`
	Priority    JobPriority `json:"priority" example:"2"`
	Progress    int         `json:"progress" example:"75"`
	CreatedAt   time.Time   `json:"created_at" example:"2023-07-18T10:30:00Z"`
	ResultCount int         `json:"result_count" example:"150"`
}

// JobStats represents statistics about jobs
type JobStats struct {
	TotalJobs     int `json:"total_jobs" example:"1250"`
	PendingJobs   int `json:"pending_jobs" example:"5"`
	RunningJobs   int `json:"running_jobs" example:"3"`
	CompletedJobs int `json:"completed_jobs" example:"1200"`
	FailedJobs    int `json:"failed_jobs" example:"42"`
}

// JobResult represents the result of a scraping job
type JobResult struct {
	JobID     string                 `json:"job_id" example:"job_123456"`
	URL       string                 `json:"url" example:"https://example.com/page1"`
	Data      map[string]interface{} `json:"data"`
	Timestamp time.Time              `json:"timestamp" example:"2023-07-18T10:45:00Z"`
	Success   bool                   `json:"success" example:"true"`
	Error     string                 `json:"error,omitempty" example:"Element not found"`
}
