version: '3.8'

services:
  truedax-scraper:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: truedax-web-scraper
    ports:
      - "9000:9000"
    environment:
      # Server Configuration
      - PORT=9000
      - GIN_MODE=release
      
      # Scraper Configuration
      - MAX_CONCURRENT_JOBS=5
      - RESULTS_DIRECTORY=/app/results
      - CLEANUP_INTERVAL=24h
      - MAX_RESULT_AGE=720h
      
      # Chrome Configuration for JavaScript scraping
      - CHROME_BIN=/usr/bin/chromium-browser
      - CHROME_PATH=/usr/bin/chromium-browser
      - CHROMIUM_FLAGS=--no-sandbox --disable-dev-shm-usage --disable-gpu --headless
    
    volumes:
      # Persist results data
      - ./data/results:/app/results
      # Optional: Mount logs directory
      - ./data/logs:/app/logs
    
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M
    
    networks:
      - scraper-network

  # Optional: Add a reverse proxy for production
  nginx:
    image: nginx:alpine
    container_name: truedax-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - truedax-scraper
    restart: unless-stopped
    networks:
      - scraper-network
    profiles:
      - production

networks:
  scraper-network:
    driver: bridge

volumes:
  results-data:
    driver: local
