package handler

import (
    "net/http"
    "sync"

    "github.com/gin-gonic/gin"
    "go-rest-api/internal/model"
)

var (
    items = make([]model.Item, 0)
    mutex = &sync.Mutex{}
)

// GetItems godoc
// @Summary      Get all items
// @Description  Retrieve all items from the system
// @Tags         items
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Success      200 {array} model.Item
// @Failure      401 {object} ErrorResponse
// @Router       /items [get]
func GetItems(c *gin.Context) {
    mutex.Lock()
    defer mutex.Unlock()

    c.JSON(http.StatusOK, items)
}

// CreateItem godoc
// @Summary      Create a new item
// @Description  Add a new item to the system
// @Tags         items
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        item body model.Item true "Item to create"
// @Success      201 {object} model.Item
// @Failure      400 {object} ErrorResponse
// @Failure      401 {object} ErrorResponse
// @Router       /items [post]
func CreateItem(c *gin.Context) {
    var newItem model.Item
    if err := c.ShouldBindJSON(&newItem); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    mutex.Lock()
    items = append(items, newItem)
    mutex.Unlock()

    c.JSON(http.StatusCreated, newItem)
}