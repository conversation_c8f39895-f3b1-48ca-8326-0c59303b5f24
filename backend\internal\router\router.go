package router

import (
    "net/http"
    "github.com/gin-gonic/gin"
    "go-rest-api/internal/handler"
    "go-rest-api/internal/service"
    "github.com/swaggo/gin-swagger"
    "github.com/swaggo/files"
    _ "go-rest-api/docs"
)

// HealthResponse represents the health check response
type HealthResponse struct {
    Status string `json:"status" example:"ok"`
}

// HealthCheck godoc
// @Summary      Health check
// @Description  Check if the API is running
// @Tags         health
// @Accept       json
// @Produce      json
// @Success      200 {object} HealthResponse
// @Router       /health [get]
func HealthCheck(c *gin.Context) {
    c.JSON(http.StatusOK, HealthResponse{Status: "ok"})
}

func SetupRouter() *gin.Engine {
    r := gin.Default()
    
    // Initialize services
    jobService := service.NewJobService()
    jobHandler := handler.NewJobHandler(jobService)
    
    // Public endpoints
    r.GET("/health", HealthCheck)
    r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler)) 
    r.POST("/login", handler.Login)

    // Protected group
    protected := r.Group("/")
    protected.Use(handler.JWTAuthMiddleware())
    
    // Legacy item endpoints (keeping for backward compatibility)
    protected.GET("/items", handler.GetItems)
    protected.POST("/items", handler.CreateItem)
    
    // Job management endpoints
    jobs := protected.Group("/jobs")
    {
        jobs.POST("", jobHandler.CreateJob)
        jobs.GET("", jobHandler.GetJobs)
        jobs.GET("/stats", jobHandler.GetJobStats)
        jobs.POST("/test-scrape", jobHandler.TestScrapeURL)
        jobs.GET("/:id", jobHandler.GetJob)
        jobs.PUT("/:id", jobHandler.UpdateJob)
        jobs.DELETE("/:id", jobHandler.DeleteJob)
        jobs.POST("/:id/cancel", jobHandler.CancelJob)
        jobs.GET("/:id/queue-position", jobHandler.GetQueuePosition)
        jobs.GET("/:id/results", jobHandler.GetJobResults)
    }

    return r
}