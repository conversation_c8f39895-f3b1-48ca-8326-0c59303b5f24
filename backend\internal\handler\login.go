package handler

import (
    "net/http"
    "github.com/gin-gonic/gin"
)

// LoginRequest represents the login request payload
type LoginRequest struct {
    Email string `json:"email" binding:"required" example:"<EMAIL>"`
}

// LoginResponse represents the login response
type LoginResponse struct {
    Token string `json:"token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
}

// ErrorResponse represents an error response
type ErrorResponse struct {
    Error string `json:"error" example:"Email required"`
}

// Login godoc
// @Summary      User login
// @Description  Authenticate user with email and get JWT token
// @Tags         auth
// @Accept       json
// @Produce      json
// @Param        request body LoginRequest true "Login credentials"
// @Success      200 {object} LoginResponse
// @Failure      400 {object} ErrorResponse
// @Failure      500 {object} ErrorResponse
// @Router       /login [post]
func Login(c *gin.Context) {
    var req LoginRequest
    if err := c.ShouldBindJSON(&req); err != nil || req.Email == "" {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Email required"})
        return
    }
    token, err := GenerateJWT(req.Email)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Could not generate token"})
        return
    }
    c.JSON(http.StatusOK, LoginResponse{Token: token})
}