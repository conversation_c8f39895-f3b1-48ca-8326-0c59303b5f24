package main

// @title           TrueDax Web Scraper API
// @version         1.0
// @description     A REST API for web scraping operations
// @termsOfService  http://swagger.io/terms/

// @contact.name   API Support
// @contact.url    http://www.swagger.io/support
// @contact.email  <EMAIL>

// @license.name  Apache 2.0
// @license.url   http://www.apache.org/licenses/LICENSE-2.0.html

// @host      localhost:9000
// @BasePath  /

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

import (
    "go-rest-api/internal/router"
    _ "go-rest-api/docs"
)

func main() {
    r := router.SetupRouter()
    r.Run(":9000")
}