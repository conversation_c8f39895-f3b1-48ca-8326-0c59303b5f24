package handler

import (
    "net/http"
    "strings"
    "github.com/gin-gonic/gin"
)

func JWTAuthMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        authHeader := c.<PERSON>eader("Authorization")
        if !strings.HasPrefix(auth<PERSON><PERSON><PERSON>, "Bearer ") {
            c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Missing or invalid Authorization header"})
            return
        }
        tokenString := strings.TrimPrefix(authHeader, "Bearer ")
        email, err := ParseJWT(tokenString)
        if err != nil {
            c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid or expired token"})
            return
        }
        // Store email in context for handlers to use
        c.Set("email", email)
        c.Next()
    }
}