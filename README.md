# TrueDax Web Scraper

A comprehensive, production-ready web scraping service with robust job management capabilities, designed for cloud deployment with Docker.

## 🚀 Quick Start with Docker

### Prerequisites
- Docker 20.10+
- Docker Compose 2.0+
- 4GB+ RAM available

### One-Command Deployment

**Windows:**
```bash
.\scripts\deploy-dev.bat
```

**Linux/macOS:**
```bash
chmod +x scripts/deploy-dev.sh
./scripts/deploy-dev.sh
```

**Manual:**
```bash
# Copy environment configuration
cp .env.example .env

# Start all services
docker-compose up --build -d

# Verify deployment
curl http://localhost:9000/health
```

### Access Your Services
- **API**: http://localhost:9000
- **Health Check**: http://localhost:9000/health  
- **Swagger Docs**: http://localhost:9000/swagger/index.html

## 🐳 Docker Features

- **Multi-stage builds** for optimized image size
- **Non-root user** for enhanced security
- **Health checks** for reliable deployments
- **Chrome support** for JavaScript-heavy sites
- **Volume persistence** for results data
- **Nginx reverse proxy** for production
- **SSL/TLS support** with Let's Encrypt integration
- **Resource limits** for controlled resource usage

## 📋 Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│     Nginx       │────│  TrueDax API    │────│   File System  │
│   (Optional)    │    │   (Go Service)  │    │   (Results)     │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        │                       │                       │
   Port 80/443              Port 9000              Volume Mount
```

## 🔧 Configuration

All configuration is handled through environment variables. Copy `.env.example` to `.env` and customize:

```bash
# Core Configuration
PORT=9000
MAX_CONCURRENT_JOBS=5

# Chrome/JavaScript Support  
CHROMIUM_FLAGS=--no-sandbox --disable-dev-shm-usage --headless

# Data Persistence
RESULTS_DIRECTORY=/app/results
CLEANUP_INTERVAL=24h
```

## 📖 Documentation

- **[Docker Deployment Guide](DOCKER-DEPLOYMENT.md)** - Complete Docker setup guide
- **[API Documentation](backend/internal/README.md)** - Detailed API reference
- **[Swagger UI](http://localhost:9000/swagger/)** - Interactive API explorer (when running)