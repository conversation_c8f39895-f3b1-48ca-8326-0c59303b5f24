# TrueDax Web Scraper - Job Management & Core Scraper

A comprehensive web scraping service with robust job management capabilities, designed for cloud deployment.

## Features

### 🚀 Job Management
- **Queue-based Processing**: Jobs are queued and processed based on priority
- **Real-time Progress Tracking**: Monitor scraping progress in real-time
- **Concurrency Control**: Configurable concurrent job execution
- **Auto-retry Logic**: Intelligent retry mechanism for failed requests
- **Result Storage**: Automatic storage and retrieval of scraping results

### 🌐 Web Scraping Capabilities
- **Static HTML Scraping**: Fast HTTP-based scraping for static content
- **JavaScript-enabled Scraping**: Headless Chrome for dynamic content
- **CSS Selector Support**: Flexible data extraction using CSS selectors
- **Pagination Detection**: Automatic detection and handling of pagination
- **Rate Limiting**: Configurable delays between requests
- **Robots.txt Compliance**: Optional robots.txt respect

### 📊 API Features
- **RESTful API**: Well-documented REST endpoints
- **JWT Authentication**: Secure user authentication
- **Swagger Documentation**: Interactive API documentation
- **Pagination Support**: Efficient handling of large result sets
- **Error Handling**: Comprehensive error reporting

## API Endpoints

### Authentication
- `POST /login` - User login and JWT token generation

### Job Management
- `POST /jobs` - Create a new scraping job
- `GET /jobs` - List user's jobs (paginated)
- `GET /jobs/{id}` - Get specific job details
- `PUT /jobs/{id}` - Update a pending job
- `DELETE /jobs/{id}` - Delete a completed job
- `POST /jobs/{id}/cancel` - Cancel a running job
- `GET /jobs/{id}/queue-position` - Get job's queue position
- `GET /jobs/{id}/results` - Get job results
- `GET /jobs/stats` - Get job statistics

### Health & Documentation
- `GET /health` - Health check
- `GET /swagger/*` - Swagger documentation

## Job Configuration

### Example Job Creation

```json
{
  "name": "E-commerce Product Scraper",
  "description": "Scrape product information from an online store",
  "config": {
    "url": "https://example-store.com/products?page=1",
    "selectors": {
      "title": "h1.product-title",
      "price": ".price-current",
      "description": ".product-description",
      "image": "img.product-image",
      "rating": ".rating-stars"
    },
    "max_pages": 10,
    "delay_ms": 1000,
    "headers": {
      "Accept-Language": "en-US,en;q=0.9"
    },
    "user_agent": "Mozilla/5.0 (compatible; TrueDaxBot/1.0)",
    "javascript_enabled": false,
    "timeout": 30
  },
  "priority": 2
}
```

### Configuration Options

| Field | Type | Description |
|-------|------|-------------|
| `url` | string | Target URL to scrape (required) |
| `selectors` | object | CSS selectors for data extraction (required) |
| `max_pages` | int | Maximum number of pages to scrape (default: 1) |
| `delay_ms` | int | Delay between requests in milliseconds (default: 0) |
| `headers` | object | Custom HTTP headers |
| `user_agent` | string | Custom User-Agent string |
| `javascript_enabled` | bool | Enable JavaScript rendering (default: false) |
| `timeout` | int | Request timeout in seconds (default: 30) |

### Priority Levels
- `1` - Low Priority
- `2` - Normal Priority (default)
- `3` - High Priority
- `4` - Critical Priority

## Job Lifecycle

```
Pending → Queued → Running → Completed/Failed/Cancelled
```

1. **Pending**: Job created but not yet queued
2. **Queued**: Job waiting in priority queue
3. **Running**: Job currently being processed
4. **Completed**: Job finished successfully
5. **Failed**: Job encountered an error
6. **Cancelled**: Job was cancelled by user

## Usage Examples

### 1. Basic Static Website Scraping

```bash
curl -X POST "http://localhost:9000/jobs" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "News Headlines",
    "config": {
      "url": "https://news-site.com",
      "selectors": {
        "headline": "h2.headline",
        "summary": ".article-summary",
        "author": ".byline",
        "publish_date": ".publish-date"
      },
      "max_pages": 1,
      "delay_ms": 500
    },
    "priority": 2
  }'
```

### 2. Multi-page E-commerce Scraping

```bash
curl -X POST "http://localhost:9000/jobs" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Product Catalog",
    "config": {
      "url": "https://shop.com/products?page=1",
      "selectors": {
        "name": ".product-name",
        "price": ".price",
        "rating": ".rating",
        "availability": ".stock-status"
      },
      "max_pages": 20,
      "delay_ms": 2000,
      "timeout": 45
    },
    "priority": 3
  }'
```

### 3. JavaScript-heavy Site Scraping

```bash
curl -X POST "http://localhost:9000/jobs" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "SPA Content",
    "config": {
      "url": "https://spa-site.com/data",
      "selectors": {
        "dynamic_content": ".js-loaded-content",
        "user_reviews": ".review-item"
      },
      "javascript_enabled": true,
      "delay_ms": 3000,
      "timeout": 60
    }
  }'
```

## Monitoring Jobs

### Check Job Status
```bash
curl -X GET "http://localhost:9000/jobs/job_12345678" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Get Queue Position
```bash
curl -X GET "http://localhost:9000/jobs/job_12345678/queue-position" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Retrieve Results
```bash
curl -X GET "http://localhost:9000/jobs/job_12345678/results" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Result Format

Results are returned in the following format:

```json
{
  "job_id": "job_12345678",
  "timestamp": "2023-07-18T10:30:00Z",
  "total_pages": 10,
  "success_rate": 95.0,
  "results": [
    {
      "url": "https://example.com/page1",
      "data": {
        "title": "Product Name",
        "price": "$99.99",
        "rating": "4.5 stars"
      },
      "timestamp": "2023-07-18T10:31:00Z",
      "success": true,
      "load_time": "1.2s"
    }
  ]
}
```

## Configuration & Deployment

### Environment Variables

```bash
# Server Configuration
PORT=9000
GIN_MODE=release

# Scraper Configuration
MAX_CONCURRENT_JOBS=5
RESULTS_DIRECTORY=./results
CLEANUP_INTERVAL=24h
MAX_RESULT_AGE=720h  # 30 days
```

### Docker Deployment

```dockerfile
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download
COPY . .
RUN go build -o app ./cmd

FROM alpine:latest
RUN apk --no-cache add ca-certificates chromium
WORKDIR /root/
COPY --from=builder /app/app .
COPY --from=builder /app/results ./results
EXPOSE 9000
CMD ["./app"]
```

## Best Practices

### 1. Rate Limiting
- Always set appropriate delays (`delay_ms`) between requests
- Start with 1-2 second delays for most sites
- Increase delays for sensitive or high-traffic sites

### 2. Resource Management
- Monitor memory usage for large scraping jobs
- Use pagination for sites with many pages
- Set reasonable timeouts to prevent hanging requests

### 3. Error Handling
- Check job status regularly during execution
- Review failed jobs to understand issues
- Implement retry logic in your client code

### 4. Respect Website Policies
- Check and respect robots.txt files
- Don't overwhelm servers with too many concurrent requests
- Use appropriate User-Agent strings

## Performance Optimization

### Scraper Options
```go
scraperOptions := scraper.ScraperOptions{
    MaxConcurrentRequests: 5,
    RequestTimeout:        30 * time.Second,
    RetryAttempts:         3,
    RetryDelay:           2 * time.Second,
    RespectRobotsTxt:     true,
    EnableCaching:        true,
    CacheExpiry:          1 * time.Hour,
    MaxMemoryUsage:       500 * 1024 * 1024, // 500MB
}
```

### Cloud Deployment Considerations
- Use container orchestration (Kubernetes, ECS)
- Implement horizontal scaling for job workers
- Use cloud storage for result files
- Set up monitoring and alerting
- Configure load balancing for high availability

## Troubleshooting

### Common Issues

1. **Jobs stuck in queue**: Check if worker processes are running
2. **High memory usage**: Reduce concurrent requests or enable result cleanup
3. **Timeout errors**: Increase timeout values or reduce page count
4. **JavaScript rendering issues**: Ensure Chrome is properly installed in container

### Monitoring
- Use the `/health` endpoint for health checks
- Monitor job statistics via `/jobs/stats`
- Check logs for error patterns
- Set up alerts for failed job rates

## Security Considerations

- Always use HTTPS in production
- Rotate JWT secrets regularly
- Implement rate limiting at the API level
- Validate and sanitize all input URLs
- Run scrapers in isolated environments
- Monitor for suspicious scraping patterns
