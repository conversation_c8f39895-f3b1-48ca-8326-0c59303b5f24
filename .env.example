# TrueDax Web Scraper Environment Configuration
# Copy this file to .env and customize the values

# Server Configuration
PORT=9000
GIN_MODE=release

# Scraper Configuration
MAX_CONCURRENT_JOBS=5
RESULTS_DIRECTORY=/app/results
CLEANUP_INTERVAL=24h
MAX_RESULT_AGE=720h

# Chrome Configuration for JavaScript scraping
CHROME_BIN=/usr/bin/chromium-browser
CHROME_PATH=/usr/bin/chromium-browser
CHROMIUM_FLAGS=--no-sandbox --disable-dev-shm-usage --disable-gpu --headless

# Rate Limiting
REQUESTS_PER_MINUTE=60
BURST_SIZE=10

# Logging
LOG_LEVEL=info
LOG_FORMAT=json

# Database (if you plan to add one later)
# DATABASE_URL=postgresql://user:password@localhost:5432/truedax_scraper

# Redis (for job queue optimization - optional)
# REDIS_URL=redis://localhost:6379/0
